
Prompt for AI agent (e.g. <PERSON>mentCode, Cursor)

📘 Project context:
  This project called "maphin" handles the task creation flow in a multi-tenant SaaS platform.
  It includes a form using **React Hook Form + Zod** for validation, and uses **React Query** for submitting to a backend API.

  **📦 Stack**:
  React + TypeScript + React Hook Form + Zod + React Query + Material UI + Prisma(Nodejs + Postgres sql), Material UI

  **📦 Local execute: **
  - MacBook 2020 Intel core i5 chip.
  - Volta for npm version manager
  - profile : maphin
  - environment: xcong ( other env is fuka, stage, prod, just use xcong is enough)


📘 Task context:
- Inquiry has comments (`InquiryComment`) with optional images (`InquiryCommentImage`) ( reference: packages/database/prisma/schema/inquiry.prisma)
- Inquiry detail is loaded via `trpc.inquiries.get.useQuery({ id })`
- Inquiry schema is versioned; comments are loaded under `inquiry.comments`
- Current frontend component is `InquiryDetailsPage` → `InquiryDetails` → `RhfInquiry`

🎯 Goal:
Add full support for comment viewing and posting on the inquiry detail page.(like github issues page)
The comment should be handled in its own API (separate from `inquiries.get`), with React Query support.

🔧 Implementation Plan:
1. **API: Create TRPC endpoint to list comments**
   - Add procedure: `trpc.inquiries.comments.list`
   - Accept `inquiryId` as input
   - Return list of `InquiryComment` with `user` (author) and `images`
   - Use `prisma.inquiryComment.findMany({ where: { inquiryId, tenantId }, ... })`

2. **API: Create TRPC mutation to post comment**
   - Procedure: `trpc.inquiries.comments.create`
   - Input: `{ inquiryId: string, body }`
   - Authenticated user will be used as `userId`
   - Save to `InquiryComment` table, optionally support images later

3. **Frontend: Add InquiryCommentList component**
   - Use `trpc.inquiries.comments.list.useQuery({ inquiryId })`
   - Render list with avatar, author name, comment body, timestamp, and images
   - Use MUI components: `Card`, `CardHeader`, `Avatar`, `CardContent`, etc...

4. **Frontend: Add InquiryCommentForm component**
   - Use React Hook Form + Zod for validation
   - Post with `trpc.inquiries.comments.create.useMutation()`
   - On success, invalidate `comments.list` query to refetch
   - Use MUI `TextField`, `Button`

5. **Compose in InquiryDetails.tsx**
   - After `<RhfInquiry readOnly />`, render:
     ```tsx
     <InquiryCommentList inquiryId={inquiry.id} />
     <InquiryCommentForm inquiryId={inquiry.id} />
     ```

🧪 Optional enhancements (can be TODO):
- Support comment image upload
- Realtime comment updates or optimistic UI

### 🧩 What AugmentCode will do:

* Generate backend procedures:

  * `/lambdas/api/src/procedures/inquiries/comments/list.ts`
  * `/lambdas/api/src/procedures/inquiries/comments/create.ts`
* Update your router file to include them
* Generate frontend hooks using `trpc.inquiries.comments.*`
* Generate new components:

  * `/features/inquiries/details/components/InquiryCommentList.tsx`
  * `/features/inquiries/details/components/InquiryCommentForm.tsx`
* Update `/features/inquiries/details/InquiryDetails.tsx` to use them



# IMPLEMENTATION PLAN

## Task 1: Create TRPC API to list comments
- [x] Create `lambdas/api/src/procedures/inquiries/comments/list.ts`
  ✅ Implemented with proper input validation using Zod
  ✅ Query includes user and images with proper filtering (deleted: false)
  ✅ Ordered by createdAt ascending for chronological display
  ✅ Return type: `InquiryCommentWithUser[]`

## Task 2: Create TRPC API to create comments
- [x] Create `lambdas/api/src/procedures/inquiries/comments/create.ts`
  ✅ Input validation: `{ inquiryId: string, body: string }` with max 1000 chars
  ✅ Mutation creates new `InquiryComment` with authenticated `userId`
  ✅ Returns created comment with user data included
  ✅ Proper tenant isolation with `tenantId`

## Task 3: Update TRPC router to include comment procedures
- [x] Update `lambdas/api/src/trpc.ts`
  ✅ Imported comment procedures: `createInquiryComment`, `listInquiryComments`
  ✅ Added to inquiries router: `comments: { list: listInquiryComments(procedure), create: createInquiryComment(procedure) }`
  ✅ Now available as `trpc.inquiries.comments.list` and `trpc.inquiries.comments.create`

## Task 4: Create InquiryCommentList component
- [x] Create `apps/web/src/features/inquiries/details/components/InquiryCommentList.tsx`
  ✅ Uses `trpc.inquiries.comments.list.useQuery({ inquiryId })` with proper loading states
  ✅ Renders with MUI `Card`, `CardHeader`, `Avatar`, `CardContent` components
  ✅ Displays: user avatar (first letter), name, comment body, formatted timestamp
  ✅ Handles loading, error, and empty states with appropriate messages
  ✅ Shows comment count in header and preserves line breaks with `whiteSpace: 'pre-wrap'`
  ✅ Placeholder for future image display functionality

## Task 5: Create InquiryCommentForm component
- [x] Create `apps/web/src/features/inquiries/details/components/InquiryCommentForm.tsx`
  ✅ Uses React Hook Form + Zod validation with proper error handling
  ✅ Schema validation: 1-1000 characters with Japanese error messages
  ✅ Uses `trpc.inquiries.comments.create.useMutation()` with error handling
  ✅ On success: invalidates comments list query + resets form automatically
  ✅ MUI `TextField` (multiline, 4 rows) + `Button` with loading states
  ✅ Proper form submission with disabled states during pending operations

## Task 6: Integrate components into InquiryDetails
- [x] Update `apps/web/src/features/inquiries/details/InquiryDetails.tsx`
  ✅ Imported comment components: `InquiryCommentList`, `InquiryCommentForm`
  ✅ Added after `<RhfInquiry />`: comment list and form in proper order
  ✅ Proper spacing and layout with MUI `Stack` (spacing={3})
  ✅ Maintains existing inquiry details layout while adding comment functionality

# 🎉 IMPLEMENTATION COMPLETE

All tasks have been successfully implemented! The inquiry comment feature is now fully functional with:

## ✅ Backend API (TRPC)
- `trpc.inquiries.comments.list` - Lists all comments for an inquiry with user data
- `trpc.inquiries.comments.create` - Creates new comments with proper validation

## ✅ Frontend Components
- `InquiryCommentList` - Displays comments with avatars, timestamps, and proper styling
- `InquiryCommentForm` - Form for posting new comments with validation and error handling
- Integrated into `InquiryDetails` page below the inquiry form

## 🚀 Features Implemented
- ✅ View all comments for an inquiry (chronological order)
- ✅ Post new comments with form validation (1-1000 characters)
- ✅ Real-time comment list updates after posting
- ✅ Proper loading states and error handling
- ✅ User avatars and formatted timestamps
- ✅ Responsive Material UI design
- ✅ Multi-tenant support with proper data isolation
- ✅ Authentication integration (comments tied to logged-in user)

## 🧪 Ready for Testing
The feature is ready for testing! Navigate to any inquiry detail page to see the comment section below the inquiry form.
