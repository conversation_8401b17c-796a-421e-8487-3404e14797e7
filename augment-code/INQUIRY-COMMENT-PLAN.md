
Prompt for AI agent (e.g. <PERSON>mentCode, Cursor)

📘 Project context:
  This project called "maphin" handles the task creation flow in a multi-tenant SaaS platform.
  It includes a form using **React Hook Form + Zod** for validation, and uses **React Query** for submitting to a backend API.

  **📦 Stack**:
  React + TypeScript + React Hook Form + Zod + React Query + Material UI + Prisma(Nodejs + Postgre sql), Material UI

  **📦 Local execute: **
  - Macbook 2020 intell chip
  - Volta for npm version manager
  - profile : maphin
  - environment: xcong ( other env is fuka, stage, prod, just use xcong is enough)


📘 Task context:
- Inquiry has comments (`InquiryComment`) with optional images (`InquiryCommentImage`) ( reference: packages/database/prisma/schema/inquiry.prisma)
- Inquiry detail is loaded via `trpc.inquiries.get.useQuery({ id })`
- Inquiry schema is versioned; comments are loaded under `inquiry.comments`
- Current frontend component is `InquiryDetailsPage` → `InquiryDetails` → `RhfInquiry`

🎯 Goal:
Add full support for comment viewing and posting on the inquiry detail page.(like github issues page)
The comment should be handled in its own API (separate from `inquiries.get`), with React Query support.

🔧 Implementation Plan:
1. **API: Create TRPC endpoint to list comments**
   - Add procedure: `trpc.inquiries.comments.list`
   - Accept `inquiryId` as input
   - Return list of `InquiryComment` with `user` (author) and `images`
   - Use `prisma.inquiryComment.findMany({ where: { inquiryId, tenantId }, ... })`

2. **API: Create TRPC mutation to post comment**
   - Procedure: `trpc.inquiries.comments.create`
   - Input: `{ inquiryId: string, body }`
   - Authenticated user will be used as `userId`
   - Save to `InquiryComment` table, optionally support images later

3. **Frontend: Add InquiryCommentList component**
   - Use `trpc.inquiries.comments.list.useQuery({ inquiryId })`
   - Render list with avatar, author name, comment body, timestamp, and images
   - Use MUI components: `Card`, `CardHeader`, `Avatar`, `CardContent`, etc...

4. **Frontend: Add InquiryCommentForm component**
   - Use React Hook Form + Zod for validation
   - Post with `trpc.inquiries.comments.create.useMutation()`
   - On success, invalidate `comments.list` query to refetch
   - Use MUI `TextField`, `Button`

5. **Compose in InquiryDetails.tsx**
   - After `<RhfInquiry readOnly />`, render:
     ```tsx
     <InquiryCommentList inquiryId={inquiry.id} />
     <InquiryCommentForm inquiryId={inquiry.id} />
     ```

🧪 Optional enhancements (can be TODO):
- Support comment image upload
- Realtime comment updates or optimistic UI

### 🧩 What AugmentCode will do:

* Generate backend procedures:

  * `/lambdas/api/src/procedures/inquiries/comments/list.ts`
  * `/lambdas/api/src/procedures/inquiries/comments/create.ts`
* Update your router file to include them
* Generate frontend hooks using `trpc.inquiries.comments.*`
* Generate new components:

  * `/features/inquiries/details/components/InquiryCommentList.tsx`
  * `/features/inquiries/details/components/InquiryCommentForm.tsx`
* Update `/features/inquiries/details/InquiryDetails.tsx` to use them



# IMPLEMENTATION PLAN
- [ ] Task 1:
