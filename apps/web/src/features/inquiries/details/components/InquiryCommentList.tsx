import { ImageAvatar } from '@/components/ImageAvatar';
import { datetimeFormatter } from '@/funcs/date';
import { Comment } from '@mui/icons-material';
import { <PERSON>, Card, CardContent, CardHeader, Stack, Typography } from '@mui/material';
import type { InquiryComments } from 'lambda-api';

type Props = {
  comments: InquiryComments;
};

export default function InquiryCommentList({ comments }: Props) {
  if (!comments || comments.length === 0) {
    return (
      <Card>
        <CardContent>
          <Box display="flex" alignItems="center" gap={1} color="text.secondary">
            <Comment />
            <Typography variant="body2">まだコメントはありません</Typography>
          </Box>
        </CardContent>
      </Card>
    );
  }

  return (
    <Stack spacing={2}>
      <Typography variant="h6" component="h3">
        コメント ({comments.length})
      </Typography>
      {comments.map((comment) => (
        <Card key={comment.id} variant="outlined">
          <CardHeader
            avatar={<ImageAvatar alt={comment.user.name} />}
            title={<Typography variant="subtitle2">{comment.user.name}</Typography>}
            subheader={
              <Typography variant="caption" color="text.secondary">
                {datetimeFormatter(new Date(comment.createdAt))}
              </Typography>
            }
          />
          <CardContent sx={{ pt: 0 }}>
            <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap' }}>
              {comment.body}
            </Typography>
            {comment.images && comment.images.length > 0 && (
              <Box mt={2}>
                <Typography variant="caption" color="text.secondary">
                  添付画像: {comment.images.length}件
                </Typography>
                {/* TODO: Implement image display */}
              </Box>
            )}
          </CardContent>
        </Card>
      ))}
    </Stack>
  );
}
