import { trpc } from '@/api';
import { LoadingSpinner } from '@/components/core/LoadingSpinner';
import { datetimeFormatter } from '@/funcs/date';
import { Comment } from '@mui/icons-material';
import { Avatar, Box, Card, CardContent, CardHeader, Stack, Typography } from '@mui/material';

type Props = {
  inquiryId: string;
};

export default function InquiryCommentList({ inquiryId }: Props) {
  const { data: comments, isLoading, error } = trpc.inquiries.comments.list.useQuery({ inquiryId });

  if (isLoading) {
    return <LoadingSpinner />;
  }

  if (error) {
    return (
      <Card>
        <CardContent>
          <Typography color="error">コメントの読み込みに失敗しました: {error.message}</Typography>
        </CardContent>
      </Card>
    );
  }

  if (!comments || comments.length === 0) {
    return (
      <Card>
        <CardContent>
          <Box display="flex" alignItems="center" gap={1} color="text.secondary">
            <Comment />
            <Typography variant="body2">まだコメントはありません</Typography>
          </Box>
        </CardContent>
      </Card>
    );
  }

  return (
    <Stack spacing={2}>
      <Typography variant="h6" component="h3">
        コメント ({comments.length})
      </Typography>
      {comments.map((comment) => (
        <Card key={comment.id} variant="outlined">
          <CardHeader
            avatar={<Avatar sx={{ bgcolor: 'primary.main' }}>{comment.user.name.charAt(0)}</Avatar>}
            title={comment.user.name}
            subheader={datetimeFormatter(new Date(comment.createdAt))}
            titleTypographyProps={{ variant: 'subtitle2' }}
            subheaderTypographyProps={{ variant: 'caption' }}
          />
          <CardContent sx={{ pt: 0 }}>
            <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap' }}>
              {comment.body}
            </Typography>
            {comment.images && comment.images.length > 0 && (
              <Box mt={2}>
                <Typography variant="caption" color="text.secondary">
                  添付画像: {comment.images.length}件
                </Typography>
                {/* TODO: Implement image display */}
              </Box>
            )}
          </CardContent>
        </Card>
      ))}
    </Stack>
  );
}
