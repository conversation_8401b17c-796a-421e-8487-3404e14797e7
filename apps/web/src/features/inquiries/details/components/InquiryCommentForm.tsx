import { trpc } from '@/api';
import { zodResolver } from '@hookform/resolvers/zod';
import { Send } from '@mui/icons-material';
import { <PERSON><PERSON>, Box, <PERSON><PERSON>, Card, CardContent, TextField, Typography } from '@mui/material';
import { useQueryClient } from '@tanstack/react-query';
import { getQueryKey } from '@trpc/react-query';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

const CommentFormSchema = z
  .object({
    body: z
      .string()
      .min(1, 'コメントを入力してください')
      .max(1000, 'コメントは1000文字以内で入力してください'),
  })
  .strict();

type CommentFormState = z.infer<typeof CommentFormSchema>;

type Props = {
  inquiryId: string;
};

export default function InquiryCommentForm({ inquiryId }: Props) {
  const queryClient = useQueryClient();

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
  } = useForm<CommentFormState>({
    resolver: zodResolver(CommentFormSchema),
    defaultValues: {
      body: '',
    },
  });

  const createCommentMutation = trpc.inquiries.comments.create.useMutation({
    onSuccess: () => {
      // Invalidate and refetch comments list
      const queryKey = getQueryKey(trpc.inquiries.comments.list, { inquiryId });
      queryClient.invalidateQueries({ queryKey });

      // Reset form
      reset();
    },
  });

  const onSubmit = (data: CommentFormState) => {
    createCommentMutation.mutate({
      inquiryId,
      body: data.body,
    });
  };

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" component="h3" gutterBottom>
          コメントを追加
        </Typography>

        {createCommentMutation.error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            コメントの投稿に失敗しました: {createCommentMutation.error.message}
          </Alert>
        )}

        <Box component="form" onSubmit={handleSubmit(onSubmit)}>
          <TextField
            {...register('body')}
            label="コメント"
            multiline
            rows={4}
            fullWidth
            variant="standard"
            error={!!errors.body}
            helperText={errors.body?.message}
            disabled={isSubmitting || createCommentMutation.isPending}
            sx={{ mb: 2 }}
          />

          <Box display="flex" justifyContent="flex-end">
            <Button
              type="submit"
              variant="contained"
              startIcon={<Send />}
              disabled={isSubmitting || createCommentMutation.isPending}
            >
              {createCommentMutation.isPending ? '投稿中...' : 'コメントを投稿'}
            </Button>
          </Box>
        </Box>
      </CardContent>
    </Card>
  );
}
