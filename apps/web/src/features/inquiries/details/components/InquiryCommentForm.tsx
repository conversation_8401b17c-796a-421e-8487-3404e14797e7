import { trpc } from '@/api';
import { zodResolver } from '@hookform/resolvers/zod';
import { Send } from '@mui/icons-material';
import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, Card, CardContent, Stack, Typography } from '@mui/material';
import { useQueryClient } from '@tanstack/react-query';
import { getQueryKey } from '@trpc/react-query';
import { RhfTextField } from 'mui-ex';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

const CommentFormSchema = z
  .object({
    body: z
      .string()
      .min(1, 'コメントを入力してください')
      .max(1000, 'コメントは1000文字以内で入力してください'),
  })
  .strict();

type CommentFormState = z.infer<typeof CommentFormSchema>;

type Props = {
  inquiryId: string;
};

export default function InquiryCommentForm({ inquiryId }: Props) {
  const queryClient = useQueryClient();

  const {
    control,
    handleSubmit,
    reset,
    formState: { isSubmitting },
  } = useForm<CommentFormState>({
    resolver: zodResolver(CommentFormSchema),
    defaultValues: {
      body: '',
    },
  });

  const { mutateAsync, error } = trpc.inquiries.comments.create.useMutation({
    onSuccess: () => {
      const queryKey = getQueryKey(trpc.inquiries.comments.list, { inquiryId });
      queryClient.invalidateQueries({ queryKey });
      reset();
    },
  });

  const onSubmit = async (data: CommentFormState) => {
    await mutateAsync({
      inquiryId,
      body: data.body,
    });
  };

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" component="h3" gutterBottom>
          コメントを追加
        </Typography>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            コメントの投稿に失敗しました: {error.message}
          </Alert>
        )}

        <Stack component="form" onSubmit={handleSubmit(onSubmit)} gap={2}>
          <RhfTextField
            control={control}
            name="body"
            label="コメント"
            multiline
            rows={4}
            disabled={isSubmitting}
          />
          <Box display="flex" justifyContent="flex-end">
            <Button type="submit" variant="contained" startIcon={<Send />} disabled={isSubmitting}>
              {isSubmitting ? '投稿中...' : 'コメントを投稿'}
            </Button>
          </Box>
        </Stack>
      </CardContent>
    </Card>
  );
}
