import { RouterButton } from '@/components/RouterButton';
import { useAppContext } from '@/contexts/app-context';
import { zodResolver } from '@hookform/resolvers/zod';
import { Edit } from '@mui/icons-material';
import { Box, Paper, Stack } from '@mui/material';
import type { Inquiry } from 'lambda-api';
import { useForm } from 'react-hook-form';
import {
  type InquiryState,
  InquiryStateSchema,
  RhfInquiry,
  inquiryToState,
} from '../common/RhfInquiry';
import InquiryCommentForm from './components/InquiryCommentForm';
import InquiryCommentList from './components/InquiryCommentList';

type Props = {
  inquiry: Inquiry;
};

export default function InquiryDetails({ inquiry }: Props) {
  const { labels } = useAppContext();
  const { control, watch } = useForm<InquiryState>({
    defaultValues: inquiryToState(inquiry),
    resolver: zodResolver(InquiryStateSchema),
  });

  return (
    <Stack spacing={3}>
      <Stack component={Paper} spacing={2} sx={{ p: 2 }}>
        <RhfInquiry control={control} watch={watch} readOnly />
        <Stack alignItems="end">
          <Box>
            <RouterButton to="/inquiries/$id/edit" params={{ id: inquiry.id }} startIcon={<Edit />}>
              {labels.action.edit}
            </RouterButton>
          </Box>
        </Stack>
      </Stack>

      <InquiryCommentList inquiryId={inquiry.id} />
      <InquiryCommentForm inquiryId={inquiry.id} />
    </Stack>
  );
}
