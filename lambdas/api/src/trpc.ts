import { setTimeout as sleep } from 'node:timers/promises';
import type { inferRouterInputs, inferRouterOutputs } from '@trpc/server';
import { TRPCError, initTRPC } from '@trpc/server';
import type { CreateAWSLambdaContextOptions } from '@trpc/server/adapters/aws-lambda';
import type { APIGatewayProxyWithCognitoAuthorizerEvent } from 'aws-lambda';
import superjson from 'superjson';
import { z } from 'zod';
import { listBicycleStyles } from './procedures/bicycle-styles/list';
import { updateBicycleStyle } from './procedures/bicycle-styles/update';

import {
  type EnvKey,
  type NonNullProps,
  bucketName,
  claimsSchema,
  debugCode,
  groupsFromClaims,
} from 'common';
import { tenantIdFromGroups } from 'models';

import { listAddress } from './procedures/address/list';
import { createBicycleName } from './procedures/bicycle-name/create';
import { deleteBicycleName } from './procedures/bicycle-name/delete';
import { getBicycleName } from './procedures/bicycle-name/get';
import { bicycleNameImportFromCsv } from './procedures/bicycle-name/import';
import { listBicycleNames } from './procedures/bicycle-name/list';
import { updateBicycleName } from './procedures/bicycle-name/update';
import { createBicycleStyle } from './procedures/bicycle-styles/create';
import { deleteBicycleStyle } from './procedures/bicycle-styles/delete';
import { getBicycleStyle } from './procedures/bicycle-styles/get';
import { bicycleStyleImportFromCsv } from './procedures/bicycle-styles/import-from-csv';
import { analysisBody } from './procedures/bicycles/ai/body';
import { cancelAnnouncement } from './procedures/bicycles/announcement/cancel';
import { startAnnouncement } from './procedures/bicycles/announcement/schedule';
import { updateAnnouncement } from './procedures/bicycles/announcement/update';
import { assessBicycle } from './procedures/bicycles/assess';
import { collectStorageFeeBicycle } from './procedures/bicycles/collect-storage-fee';
import { copyOwnerInfo } from './procedures/bicycles/copy-owner-info';
import { countBicycles } from './procedures/bicycles/count';
import { createBicycle } from './procedures/bicycles/create';
import { cancelDeadline } from './procedures/bicycles/deadline/cancel';
import { scheduleDeadline } from './procedures/bicycles/deadline/schedule';
import { updateDeadline } from './procedures/bicycles/deadline/update';
import { editBody } from './procedures/bicycles/edit/body';
import { editEvent } from './procedures/bicycles/edit/event';
import { editLocation } from './procedures/bicycles/edit/location';
import { editOwner } from './procedures/bicycles/edit/owner';
import { getBicycle } from './procedures/bicycles/get';
import { getBicycleBySerialNo } from './procedures/bicycles/get-by-serial-no';
import { imageAnalysis } from './procedures/bicycles/imageAnalysis';
import { keepExtend } from './procedures/bicycles/keep-extend';
import { listBicycles } from './procedures/bicycles/list';
import { cancelNotification } from './procedures/bicycles/notification/cancel';
import { updateNotification } from './procedures/bicycles/notification/update';
import { notify } from './procedures/bicycles/notify';
import { patrol } from './procedures/bicycles/patrol';
import { resetNotificationDate } from './procedures/bicycles/reset-notification-date';
import { returnToOwnerBicycle } from './procedures/bicycles/return-to-owner';
import { storeBicycle } from './procedures/bicycles/store';
import { createBicycleColor } from './procedures/color/create';
import { deleteBicycleColor } from './procedures/color/delete';
import { getBicycleColor } from './procedures/color/get';
import { listBicycleColors } from './procedures/color/list';
import { updateBicycleColor } from './procedures/color/update';
import { createCondition } from './procedures/conditions/create';
import { getCondition } from './procedures/conditions/get';
import { conditionImportFromCsv } from './procedures/conditions/import';
import { updateCondition } from './procedures/conditions/update';
import { cancelDisposeContract } from './procedures/contracts/dispose/cancel';
import { createDisposeContract } from './procedures/contracts/dispose/create';
import { getDisposesContract } from './procedures/contracts/dispose/get';
import { listDisposeContracts } from './procedures/contracts/dispose/list';
import { updateDisposeContract } from './procedures/contracts/dispose/update';
import { cancelSellContract } from './procedures/contracts/sell/cancel';
import { createSellContract } from './procedures/contracts/sell/create';
import { getSellContract } from './procedures/contracts/sell/get';
import { listSellContracts } from './procedures/contracts/sell/list';
import { updateSellContract } from './procedures/contracts/sell/update';
import { cancelTransferContract } from './procedures/contracts/transfer/cancel';
import { createTransferContract } from './procedures/contracts/transfer/create';
import { getTransfersContract } from './procedures/contracts/transfer/get';
import { listTransferContracts } from './procedures/contracts/transfer/list';
import { updateTransferContract } from './procedures/contracts/transfer/update';
import { createDealer } from './procedures/dealers/create';
import { getDealer } from './procedures/dealers/get';
import { dealerImportFromCsv } from './procedures/dealers/import';
import { listDealers } from './procedures/dealers/list';
import { createDltbContact } from './procedures/dltb-contacts/create';
import { getDltbContact } from './procedures/dltb-contacts/get';
import { dltbContactImportFromCsv } from './procedures/dltb-contacts/import';
import { listDltbContacts } from './procedures/dltb-contacts/list';
import { updateDltbContact } from './procedures/dltb-contacts/update';
import { createHoliday } from './procedures/holidays/create';
import { deleteHoliday } from './procedures/holidays/delete';
import { getHoliday } from './procedures/holidays/get';
import { holidayImportFromCsv } from './procedures/holidays/import-from-csv';
import { listHolidays } from './procedures/holidays/list';
import { updateHoliday } from './procedures/holidays/update';
import { createInquiryComment } from './procedures/inquiries/comments/create';
import { listInquiryComments } from './procedures/inquiries/comments/list';
import { createInquiry } from './procedures/inquiries/create';
import { getInquiry } from './procedures/inquiries/get';
import { listInquiry } from './procedures/inquiries/list';
import { updateInquiry } from './procedures/inquiries/update';
import { listLabels } from './procedures/labels/list';
import { updateLabel } from './procedures/labels/update';
import { createLandmark } from './procedures/landmarks/create';
import { getLandmark } from './procedures/landmarks/get';
import { landmarkImportFromCsv } from './procedures/landmarks/import';
import { updateLandmark } from './procedures/landmarks/update';
import { createMaker } from './procedures/maker/create';
import { deleteMaker } from './procedures/maker/delete';
import { getMaker } from './procedures/maker/get';
import { makerImportFromCsv } from './procedures/maker/import';
import { listMakers } from './procedures/maker/list';
import { updateMaker } from './procedures/maker/update';
import { createMarker } from './procedures/markers/create';
import { listMarkers } from './procedures/markers/list';
import { updateMarkerStatus } from './procedures/markers/updateStatus';
import { createMotorizedBicycleNumberPlateContact } from './procedures/motorized-bicycle-number-plate-contact/create';
import { getMotorizedBicycleNumberPlateContact } from './procedures/motorized-bicycle-number-plate-contact/get';
import { motorizedBicycleNumberPlateContactImportFromCsv } from './procedures/motorized-bicycle-number-plate-contact/import';
import { listMotorizedBicycleNumberPlateContacts } from './procedures/motorized-bicycle-number-plate-contact/list';
import { updateMotorizedBicycleNumberPlateContact } from './procedures/motorized-bicycle-number-plate-contact/update';
import { hideNotification } from './procedures/notifications/hide';
import { listNotifications } from './procedures/notifications/list';
import { readNotification } from './procedures/notifications/read';
import { readAllNotifications } from './procedures/notifications/read-all';
import { createNumberPlateLocation } from './procedures/number-plate-locations/create';
import { deleteNumberPlateLocation } from './procedures/number-plate-locations/delete';
import { getNumberPlateLocation } from './procedures/number-plate-locations/get';
import { numberPlateLocationImportFromCsv } from './procedures/number-plate-locations/import';
import { updateNumberPlateLocation } from './procedures/number-plate-locations/update';
import { getRecipient } from './procedures/owners/get';
import { listRecipients } from './procedures/owners/list';
import { createParking } from './procedures/parking/create';
import { deleteParking } from './procedures/parking/delete';
import { getParking } from './procedures/parking/get';
import { parkingImportFromCsv } from './procedures/parking/import';
import { listParkings } from './procedures/parking/list';
import { updateParking } from './procedures/parking/update';
import { cancelPoliceReference } from './procedures/police-references/cancel';
import { getPoliceReference } from './procedures/police-references/get';
import { listPoliceReferences } from './procedures/police-references/list';
import { receivePoliceReferenceResponse } from './procedures/police-references/receive-response';
import { policeReferenceOwners } from './procedures/police-references/reference-owners';
import { requestPoliceReference } from './procedures/police-references/request';
import { createPoliceStation } from './procedures/police-stations/create';
import { getPoliceStation } from './procedures/police-stations/get';
import { policeStationImportFromCsv } from './procedures/police-stations/import';
import { settingDefaultPoliceStation } from './procedures/police-stations/setting-default';
import { updatePoliceStation } from './procedures/police-stations/update';
import { createPostalCode } from './procedures/postal-codes/create';
import { deletePostalCode } from './procedures/postal-codes/delete';
import { getPostalCode } from './procedures/postal-codes/get';
import { listPostalCodesByPrefecture } from './procedures/postal-codes/list';
import { settingDefaultPrefecture } from './procedures/postal-codes/setting-default-prefecture';
import { getAvatarPresignedUrl } from './procedures/presigned-urls/avatar';
import { listBicyclePresignedUrls } from './procedures/presigned-urls/bicycle';
import { bicycleStylePresignedUrls } from './procedures/presigned-urls/bicycle-style';
import { listGuidePresignedUrls } from './procedures/presigned-urls/guide';
import { listInquiryPresignedUrls } from './procedures/presigned-urls/inquiry';
import { listLandmarkPresignedUrls } from './procedures/presigned-urls/landmark';
import { listMarkerPresignedUrls } from './procedures/presigned-urls/marker';
import { listStoragePresignedUrls } from './procedures/presigned-urls/storage/list/storage';
import { getMapImagePresignedUrl } from './procedures/presigned-urls/storage/map/get';
import { getTeamLogoPresignedUrl } from './procedures/presigned-urls/team-logo';
import { getTenantLogoPresignedUrl } from './procedures/presigned-urls/tenant-logo';
import { createPriceSuggestion } from './procedures/price-suggestions/create';
import { deletePriceSuggestion } from './procedures/price-suggestions/delete';
import { getPriceSuggestion } from './procedures/price-suggestions/get';
import { listPriceSuggestions } from './procedures/price-suggestions/list';
import { updatePriceSuggestion } from './procedures/price-suggestions/update';
import { listReceptionRoute } from './procedures/reception-routes/list/listReceptionRoute';
import { createRegistrationNumberContact } from './procedures/registration-number-contacts/create';
import { deleteRegistrationNumberContact } from './procedures/registration-number-contacts/delete';
import { getRegistrationNumberContact } from './procedures/registration-number-contacts/get';
import { registrationNumberContactImportFromCsv } from './procedures/registration-number-contacts/import';
import { listRegistrationNumberContact } from './procedures/registration-number-contacts/list';
import { updateRegistrationNumberContact } from './procedures/registration-number-contacts/update';
import { getRegistrationNumber } from './procedures/registration-numbers/get';
import { createReleaseTag } from './procedures/release-tag/create';
import { deleteReleaseTag } from './procedures/release-tag/delete';
import { getReleaseTag } from './procedures/release-tag/get';
import { releaseTagImportFromCsv } from './procedures/release-tag/import';
import { listReleaseTags } from './procedures/release-tag/list';
import { updateReleaseTag } from './procedures/release-tag/update';
import { createRole } from './procedures/roles/create';
import { getRole } from './procedures/roles/get';
import { listRoles } from './procedures/roles/list';
import { selectRole } from './procedures/roles/select';
import { updateRole } from './procedures/roles/update';
import { createSerialTag } from './procedures/serial-tags/create';
import { listBicycleSerialTags } from './procedures/serial-tags/list';
import { printSerialTag } from './procedures/serial-tags/print';
import { countAnnualBicyclesOfStoreStatus } from './procedures/statistics/count-annual-bicycles-of-store-status';
import { countByLandmark } from './procedures/statistics/count-by-landmark';
import { countMonthlyStatus } from './procedures/statistics/count-monthly-status';
import { countRemoveToReturnStatusByYear } from './procedures/statistics/count-remove-to-return-status-by-year';
import { dailyReport } from './procedures/statistics/daily-report';
import { releasedCycles } from './procedures/statistics/releasedCycles';
import { returnToOwnerHistory } from './procedures/statistics/return-to-owner-history';
import { listTheftBicycles } from './procedures/statistics/theft-bicycles';
import { countByStorage } from './procedures/storages/count-current-bicycles-by-storage';
import { createStorage } from './procedures/storages/create';
import { getStorage } from './procedures/storages/get';
import { storageImportFromCsv } from './procedures/storages/import';
import { searchBicycles } from './procedures/storages/searchBicycles';
import { updateStorage } from './procedures/storages/update';
import { listAccessLogs } from './procedures/system/access-logs/list';
import { createTeam } from './procedures/teams/create';
import { getTeam } from './procedures/teams/get';
import { listTeams } from './procedures/teams/list';
import { updateTeam } from './procedures/teams/update';
import { getTenant } from './procedures/tenant/get';
import { updateNotificationSettings } from './procedures/tenant/settings/keep/notification';
import { updateReturnToOwnerSettings } from './procedures/tenant/settings/keep/return-to-owner';
import { updateSerialTagSettings } from './procedures/tenant/settings/keep/serial-tag';
import { updateStorageFeeSettings } from './procedures/tenant/settings/keep/storage-fee';
import { updateStoreSettings } from './procedures/tenant/settings/keep/store';
import { updateEnsureAbandonedSettings } from './procedures/tenant/settings/patrol/ensure-abandoned';
import { updateFindSettings } from './procedures/tenant/settings/patrol/find';
import { updatePatrolFlow } from './procedures/tenant/settings/patrol/flow';
import { updateMarkSettings } from './procedures/tenant/settings/patrol/mark';
import { updateRemoveSettings } from './procedures/tenant/settings/patrol/remove';
import { createSearchSet } from './procedures/tenant/settings/search-sets/create';
import { listSearchSets } from './procedures/tenant/settings/search-sets/list';
import { updateSearchSet } from './procedures/tenant/settings/search-sets/update';
import { updateTenant } from './procedures/tenant/update';
import { createUser } from './procedures/users/create';
import { getUser } from './procedures/users/get';
import { listUser } from './procedures/users/list';
import { updateUser } from './procedures/users/update';
import { type Clients, type Context, type TRPCInstance, TRPC_ERROR_CODES } from './types';

export const createTRPC = () => initTRPC.context<Context>().create({ transformer: superjson });

type CreateContextArgs = Clients & { envKey: EnvKey };
type Options = CreateAWSLambdaContextOptions<APIGatewayProxyWithCognitoAuthorizerEvent>;

export const createContext =
  ({ rlsClientProvider, envKey, ...clients }: CreateContextArgs) =>
  ({ event }: Options): Context => {
    console.log(`\nEVENT: \n${JSON.stringify(event, null, 2)}`);
    const claims = claimsSchema.parse(event.requestContext.authorizer.claims);
    const username = claims['cognito:username'];
    console.log(`\nUSERNAME: ${username}`);
    const groups = groupsFromClaims(claims);
    console.log(`\nGROUPS:\n${JSON.stringify(groups, null, 2)}`);
    const tenantId = tenantIdFromGroups(groups);
    if (tenantId === undefined) throw new TRPCError({ code: 'FORBIDDEN' });
    console.log(`\nTENANT ID: ${tenantId}`);

    const readyRls = rlsClientProvider.setTenantId(tenantId);
    const Bucket = bucketName(envKey);
    return { ...clients, ...readyRls, userId: username, tenantId, Bucket, log: true };
  };

export const createRouter = (t: TRPCInstance) => {
  const procedure = t.procedure.use(async (request) => {
    const { next, path, type, getRawInput } = request;
    const input = await getRawInput();
    console.log(`\nINPUT:\n${JSON.stringify({ path, type, input }, null, 2)}`);
    if (input && JSON.stringify(input).includes('"debug":true')) console.log(debugCode);
    return next();
  });
  return t.router({
    address: { list: listAddress(procedure) },
    bicycles: {
      ai: {
        body: analysisBody(procedure),
      },
      list: listBicycles(procedure),
      count: countBicycles(procedure),
      get: getBicycle(procedure),
      getBySerialNo: getBicycleBySerialNo(procedure),
      patrol: patrol(procedure),
      store: storeBicycle(procedure),
      notify: notify(procedure),
      resetNotificationDate: resetNotificationDate(procedure),
      copyOwnerInfo: copyOwnerInfo(procedure),
      returnToOwner: returnToOwnerBicycle(procedure),
      collectStorageFee: collectStorageFeeBicycle(procedure),
      imageAnalysis: imageAnalysis(procedure),
      notification: {
        update: updateNotification(procedure),
        cancel: cancelNotification(procedure),
      },
      announcement: {
        start: startAnnouncement(procedure),
        update: updateAnnouncement(procedure),
        cancel: cancelAnnouncement(procedure),
      },
      deadline: {
        schedule: scheduleDeadline(procedure),
        update: updateDeadline(procedure),
        cancel: cancelDeadline(procedure),
      },
      create: createBicycle(procedure),
      edit: {
        location: editLocation(procedure),
        body: editBody(procedure),
        owner: editOwner(procedure),
        event: editEvent(procedure),
      },
      extend: keepExtend(procedure),
      assess: assessBicycle(procedure),
    },
    conditions: {
      get: getCondition(procedure),
      create: createCondition(procedure),
      update: updateCondition(procedure),
      import: conditionImportFromCsv(procedure),
    },
    contracts: {
      dispose: {
        list: listDisposeContracts(procedure),
        get: getDisposesContract(procedure),
        create: createDisposeContract(procedure),
        update: updateDisposeContract(procedure),
        cancel: cancelDisposeContract(procedure),
      },
      sell: {
        list: listSellContracts(procedure),
        get: getSellContract(procedure),
        create: createSellContract(procedure),
        update: updateSellContract(procedure),
        cancel: cancelSellContract(procedure),
      },
      transfer: {
        list: listTransferContracts(procedure),
        get: getTransfersContract(procedure),
        create: createTransferContract(procedure),
        update: updateTransferContract(procedure),
        cancel: cancelTransferContract(procedure),
      },
    },
    dealers: {
      list: listDealers(procedure),
      get: getDealer(procedure),
      create: createDealer(procedure),
      import: dealerImportFromCsv(procedure),
    },

    inquiries: {
      list: listInquiry(procedure),
      get: getInquiry(procedure),
      create: createInquiry(procedure),
      update: updateInquiry(procedure),
      comments: {
        list: listInquiryComments(procedure),
        create: createInquiryComment(procedure),
      },
    },
    labels: {
      list: listLabels(procedure),
      update: updateLabel(procedure),
    },
    landmarks: {
      get: getLandmark(procedure),
      create: createLandmark(procedure),
      update: updateLandmark(procedure),
      import: landmarkImportFromCsv(procedure),
    },
    parkings: {
      list: listParkings(procedure),
      get: getParking(procedure),
      create: createParking(procedure),
      update: updateParking(procedure),
      delete: deleteParking(procedure),
      import: parkingImportFromCsv(procedure),
    },
    releaseTags: {
      list: listReleaseTags(procedure),
      get: getReleaseTag(procedure),
      create: createReleaseTag(procedure),
      update: updateReleaseTag(procedure),
      delete: deleteReleaseTag(procedure),
      import: releaseTagImportFromCsv(procedure),
    },
    holidays: {
      list: listHolidays(procedure),
      get: getHoliday(procedure),
      create: createHoliday(procedure),
      update: updateHoliday(procedure),
      delete: deleteHoliday(procedure),
      import: holidayImportFromCsv(procedure),
    },
    makers: {
      list: listMakers(procedure),
      get: getMaker(procedure),
      create: createMaker(procedure),
      update: updateMaker(procedure),
      delete: deleteMaker(procedure),
      import: makerImportFromCsv(procedure),
    },
    markers: {
      list: listMarkers(procedure),
      create: createMarker(procedure),
      updateStatus: updateMarkerStatus(procedure),
    },
    notifications: {
      list: listNotifications(procedure),
      read: readNotification(procedure),
      readAll: readAllNotifications(procedure),
      hide: hideNotification(procedure),
    },
    numberPlateLocations: {
      get: getNumberPlateLocation(procedure),
      create: createNumberPlateLocation(procedure),
      update: updateNumberPlateLocation(procedure),
      delete: deleteNumberPlateLocation(procedure),
      import: numberPlateLocationImportFromCsv(procedure),
    },
    motorizedBicycleNumberPlateContacts: {
      list: listMotorizedBicycleNumberPlateContacts(procedure),
      get: getMotorizedBicycleNumberPlateContact(procedure),
      create: createMotorizedBicycleNumberPlateContact(procedure),
      update: updateMotorizedBicycleNumberPlateContact(procedure),
      import: motorizedBicycleNumberPlateContactImportFromCsv(procedure),
    },
    dltbContacts: {
      list: listDltbContacts(procedure),
      get: getDltbContact(procedure),
      create: createDltbContact(procedure),
      update: updateDltbContact(procedure),
      import: dltbContactImportFromCsv(procedure),
    },
    priceSuggestions: {
      list: listPriceSuggestions(procedure),
      get: getPriceSuggestion(procedure),
      create: createPriceSuggestion(procedure),
      update: updatePriceSuggestion(procedure),
      delete: deletePriceSuggestion(procedure),
    },
    bicycleStyles: {
      list: listBicycleStyles(procedure),
      get: getBicycleStyle(procedure),
      create: createBicycleStyle(procedure),
      update: updateBicycleStyle(procedure),
      delete: deleteBicycleStyle(procedure),
      import: bicycleStyleImportFromCsv(procedure),
    },
    policeReferences: {
      list: listPoliceReferences(procedure),
      get: getPoliceReference(procedure),
      request: requestPoliceReference(procedure),
      receiveResponse: receivePoliceReferenceResponse(procedure),
      cancel: cancelPoliceReference(procedure),
      referenceOwners: policeReferenceOwners(procedure),
    },
    policeStations: {
      get: getPoliceStation(procedure),
      create: createPoliceStation(procedure),
      update: updatePoliceStation(procedure),
      settingDefault: settingDefaultPoliceStation(procedure),
      import: policeStationImportFromCsv(procedure),
    },
    registrationNumberContacts: {
      list: listRegistrationNumberContact(procedure),
      get: getRegistrationNumberContact(procedure),
      create: createRegistrationNumberContact(procedure),
      update: updateRegistrationNumberContact(procedure),
      delete: deleteRegistrationNumberContact(procedure),
      import: registrationNumberContactImportFromCsv(procedure),
    },
    bicycleNames: {
      list: listBicycleNames(procedure),
      get: getBicycleName(procedure),
      create: createBicycleName(procedure),
      update: updateBicycleName(procedure),
      delete: deleteBicycleName(procedure),
      import: bicycleNameImportFromCsv(procedure),
    },
    colors: {
      list: listBicycleColors(procedure),
      get: getBicycleColor(procedure),
      create: createBicycleColor(procedure),
      update: updateBicycleColor(procedure),
      delete: deleteBicycleColor(procedure),
    },
    owners: {
      list: listRecipients(procedure),
      get: getRecipient(procedure),
    },
    postalCodes: {
      list: listPostalCodesByPrefecture(procedure),
      get: getPostalCode(procedure),
      create: createPostalCode(procedure),
      delete: deletePostalCode(procedure),
      settingDefaultPrefecture: settingDefaultPrefecture(procedure),
    },
    presignedUrls: {
      avatar: { get: getAvatarPresignedUrl(procedure) },
      bicycle: { list: listBicyclePresignedUrls(procedure) },
      guide: { list: listGuidePresignedUrls(procedure) },
      landmark: { list: listLandmarkPresignedUrls(procedure) },
      marker: { list: listMarkerPresignedUrls(procedure) },
      storage: {
        list: listStoragePresignedUrls(procedure),
        map: { get: getMapImagePresignedUrl(procedure) },
      },
      bicycleStyle: bicycleStylePresignedUrls(procedure),
      teamLogo: { get: getTeamLogoPresignedUrl(procedure) },
      inquiry: { list: listInquiryPresignedUrls(procedure) },
      tenantLogo: { get: getTenantLogoPresignedUrl(procedure) },
    },
    receptionRoutes: {
      list: listReceptionRoute(procedure),
    },
    roles: {
      list: listRoles(procedure),
      get: getRole(procedure),
      create: createRole(procedure),
      update: updateRole(procedure),
      select: selectRole(procedure),
    },
    registrationNumbers: {
      get: getRegistrationNumber(procedure),
    },
    serialTags: {
      list: listBicycleSerialTags(procedure),
      create: createSerialTag(procedure),
      print: printSerialTag(procedure),
    },
    statistics: {
      countAnnualBicyclesOfStoreStatus: countAnnualBicyclesOfStoreStatus(procedure),
      countMonthlyStatus: countMonthlyStatus(procedure),
      dailyReport: dailyReport(procedure),
      countRemoveToReturnStatusByYear: countRemoveToReturnStatusByYear(procedure),
      countByLandmark: countByLandmark(procedure),
      theft: listTheftBicycles(procedure),
      returnToOwnerHistory: returnToOwnerHistory(procedure),
      releasedCycles: releasedCycles(procedure),
    },
    storages: {
      get: getStorage(procedure),
      create: createStorage(procedure),
      update: updateStorage(procedure),
      count: countByStorage(procedure),
      searchBicycles: searchBicycles(procedure),
      import: storageImportFromCsv(procedure),
    },
    system: {
      accessLogs: {
        list: listAccessLogs(procedure),
      },
    },
    teams: {
      list: listTeams(procedure),
      get: getTeam(procedure),
      create: createTeam(procedure),
      update: updateTeam(procedure),
    },
    tenant: {
      get: getTenant(procedure),
      update: updateTenant(procedure),
      settings: {
        searchSets: {
          list: listSearchSets(procedure),
          create: createSearchSet(procedure),
          update: updateSearchSet(procedure),
        },
        // tenant.settings 以下はすべて update 操作なので update を省略しています
        patrol: {
          flow: updatePatrolFlow(procedure),
          mark: updateMarkSettings(procedure),
          find: updateFindSettings(procedure),
          ensureAbandoned: updateEnsureAbandonedSettings(procedure),
          remove: updateRemoveSettings(procedure),
        },
        keep: {
          notification: updateNotificationSettings(procedure),
          serialTag: updateSerialTagSettings(procedure),
          store: updateStoreSettings(procedure),
          returnToOwner: updateReturnToOwnerSettings(procedure),
          storageFee: updateStorageFeeSettings(procedure),
        },
      },
    },
    users: {
      list: listUser(procedure),
      get: getUser(procedure),
      create: createUser(procedure),
      update: updateUser(procedure),
    },
    console: {
      log: procedure.input(z.object({ message: z.string() }).strict()).mutation(({ input }) => {
        console.log(input.message);
        return input.message;
      }),
      err: procedure
        .input(z.object({ code: z.enum(TRPC_ERROR_CODES), message: z.string() }).strict())
        .mutation(({ input }) => {
          throw new TRPCError(input);
        }),
    },
    sleep: procedure.input(z.object({ sec: z.number() }).strict()).mutation(async ({ input }) => {
      console.log(`sleep: ${input.sec}`);
      await sleep(input.sec * 1000);
      return input.sec;
    }),
  });
};

export type AppRouter = ReturnType<typeof createRouter>;
export type RouterInputs = inferRouterInputs<AppRouter>;
export type RouterOutputs = inferRouterOutputs<AppRouter>;

export type Tenant = NonNullProps<RouterOutputs['tenant']['get']>;

export type User = RouterOutputs['users']['list'][number];
export type Team = RouterOutputs['teams']['get'];
export type Role = RouterOutputs['roles']['list'][number];
export type SearchSet = RouterOutputs['tenant']['settings']['searchSets']['list'][number];
export type MapSettings = Tenant['map'];
export type Landmark = Tenant['landmarks'][number];
export type Parking = RouterOutputs['parkings']['list'][number];
export type Parkings = RouterOutputs['parkings']['list'];
export type ReleaseTag = RouterOutputs['releaseTags']['get'];
export type ReleaseTags = RouterOutputs['releaseTags']['list'];
export type Maker = RouterOutputs['makers']['list'][number];
export type Holiday = RouterOutputs['holidays']['get'];
type StorageBase = Tenant['storages'][number];

export type Storage = Omit<StorageBase, 'mapImage'> & {
  mapImage: StorageBase['mapImage'] | null;
};
export type StorageStatus = RouterOutputs['storages']['count'][number];
export type LandmarkCountStatistic = RouterOutputs['statistics']['countByLandmark'][number];
export type Condition = Tenant['conditions'][number];
export type PoliceStation = Tenant['policeStations'][number];
export type PriceSuggestion = Tenant['priceSuggestions'][number];
export type NumberPlateLocation = Tenant['numberPlateLocations'][number];
export type MotorizedBicycleNumberPlateContact =
  RouterOutputs['motorizedBicycleNumberPlateContacts']['get'];
export type MotorizedBicycleNumberPlateContacts =
  RouterOutputs['motorizedBicycleNumberPlateContacts']['list'];
export type DltbContact = RouterOutputs['dltbContacts']['get'];
export type DltbContacts = RouterOutputs['dltbContacts']['list'];
export type BicycleStyle = RouterOutputs['bicycleStyles']['get'];
export type PostalCode = RouterOutputs['postalCodes']['list'][number];
export type RegistrationNumberContact = RouterOutputs['registrationNumberContacts']['list'][number];
export type RegistrationNumberContacts = RouterOutputs['registrationNumberContacts']['list'];
export type Recipient = RouterOutputs['owners']['list'][number];
export type PostalAddress = RouterOutputs['address']['list'][number];

export type Model = Markers[number]['model'] | Bicycles[number]['model'];
export type Markers = RouterOutputs['markers']['list'];
export type Bicycles = RouterOutputs['bicycles']['list'];
export type Bicycle = NonNullable<RouterOutputs['bicycles']['get']>;
export type BypassToLast<K extends 'location' | 'body' | 'owner' | 'storageLocation'> = NonNullable<
  NonNullable<Bicycles[number][K]>['last']
>;
export type BicycleLocation = BypassToLast<'location'>;
export type BicycleBody = BypassToLast<'body'>;
export type BicycleStorageLocation = BypassToLast<'storageLocation'>;
export type BicycleSerialTag = NonNullable<Bicycles[number]['serialTag']>;
export type BicycleOwner = BypassToLast<'owner'>;
export type BicycleOwnerWithVersions = NonNullable<Bicycle['owner']>;

export type BicycleEvent = Bicycles[number]['events'][number];
export type BicycleImage = BicycleEvent['images'][number];

export type PoliceReferences = RouterOutputs['policeReferences']['list'];
export type PoliceReference = RouterOutputs['policeReferences']['get'];

export type {
  NotificationStatus,
  AnnouncementStatus,
  DeadlineStatus,
  ReferenceStatus,
  ReleaseContractStatus,
} from './bicycle-funcs';

export type SellContracts = RouterOutputs['contracts']['sell']['list'];
export type SellContract = RouterOutputs['contracts']['sell']['get'];

export type DisposeContracts = RouterOutputs['contracts']['dispose']['list'];
export type DisposeContract = RouterOutputs['contracts']['dispose']['get'];

export type TransferContracts = RouterOutputs['contracts']['transfer']['list'];
export type TransferContract = RouterOutputs['contracts']['transfer']['get'];

export type BicycleDealers = RouterOutputs['dealers']['list'];
export type BicycleDealer = RouterOutputs['dealers']['get'];

export type Inquiries = RouterOutputs['inquiries']['list'];
export type Inquiry = RouterOutputs['inquiries']['get'];
export type InquiryComments = RouterOutputs['inquiries']['comments']['list'];

export type PoliceReferenceOwners = RouterOutputs['policeReferences']['referenceOwners'];
export type AccessLog = RouterOutputs['system']['accessLogs']['list'][number];
export type BicycleName = RouterOutputs['bicycleNames']['get'];
export type BicycleNames = RouterOutputs['bicycleNames']['list'];
export type BicycleColors = RouterOutputs['colors']['list'];
export type BicycleColor = RouterOutputs['colors']['get'];
