import { z } from 'zod';
import type { Context, TRPCProcedure } from '../../../types';

const InputSchema = z
  .object({
    inquiryId: z.string().uuid(),
    body: z.string().min(1).max(1000),
  })
  .strict();

type Input = z.infer<typeof InputSchema>;
type MutationArgs = { input: Input; ctx: Context };

export const createInquiryCommentMutation = async ({
  input: { inquiryId, body },
  ctx: { prisma, userId },
}: MutationArgs) => {
  const comment = await prisma.inquiryComment.create({
    data: {
      inquiryId,
      body,
      // images TODO
      userId,
    },
    include: {
      user: true,
      images: {
        where: { deleted: false },
        orderBy: { sortOrder: 'asc' },
      },
    },
  });

  return comment;
};

export const createInquiryComment = (p: TRPCProcedure) =>
  p.input(InputSchema).mutation(createInquiryCommentMutation);
